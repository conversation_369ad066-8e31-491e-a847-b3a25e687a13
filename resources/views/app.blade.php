<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RELIFE Medical Technologies</title>
    <meta name="description" content="Professional prosthetic devices and medical solutions. Quality, comfort, and innovation for enhanced mobility.">
    <meta name="author" content="RELIFE Medical Technologies">
    <meta name="keywords" content="prosthetics, medical devices, mobility solutions, artificial limbs, orthotics">

    <meta property="og:title" content="RELIFE Medical Technologies - Premium Prosthetic Solutions">
    <meta property="og:description" content="Professional prosthetic devices and medical solutions. Quality, comfort, and innovation for enhanced mobility.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ config('app.url') }}">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@relife_medical">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Google reCAPTCHA -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>

    <!-- Production React Assets -->
    @if(file_exists(public_path('build/assets')))
        @php
            $manifest = json_decode(file_get_contents(public_path('build/.vite/manifest.json')), true);
            $cssFile = null;
            $jsFile = null;

            foreach ($manifest as $key => $asset) {
                if (isset($asset['isEntry']) && $asset['isEntry']) {
                    if (isset($asset['css']) && count($asset['css']) > 0) {
                        $cssFile = $asset['css'][0];
                    }
                    $jsFile = $asset['file'];
                }
            }
        @endphp

        @if($cssFile)
            <link rel="stylesheet" href="{{ asset('build/assets/' . basename($cssFile)) }}">
        @endif
    @else
        <!-- Fallback: Serve pre-built index.html directly -->
        <script>
            window.location.href = '/build/index.html';
        </script>
    @endif
</head>
<body>
    <div id="root"></div>

    @if(file_exists(public_path('build/assets')) && isset($jsFile))
        <script type="module" src="{{ asset('build/assets/' . basename($jsFile)) }}"></script>
    @endif
</body>
</html>
