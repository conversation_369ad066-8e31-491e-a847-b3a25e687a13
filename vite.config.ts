import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import laravel from 'laravel-vite-plugin';
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  // Define the root directory for the frontend source
  root: 'resources/react',

  // Build configuration
  build: {
    // Output directory relative to the project root (Laravel public/build directory)
    outDir: '../../public/build',

    // Don't empty the entire public directory (preserve Laravel assets)
    emptyOutDir: true,

    // Generate manifest for Laravel integration
    manifest: true,

    // Target modern browsers
    target: 'es2015',

    // Ensure source maps are generated for debugging
    sourcemap: mode === 'development'
  },
  
  // Development server configuration
  server: {
    host: "0.0.0.0",
    port: 3000,
    
    // Proxy API requests to Lara<PERSON> during development
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/storage': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/admin': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  
  plugins: [
    laravel({
      input: ['resources/react/src/main.tsx'],
      refresh: true,
    }),
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  
  // Path resolution
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "resources/react/src"),
    },
  },
  
  // Define global constants
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  },
  
  // CSS configuration
  css: {
    postcss: './postcss.config.js'
  }
}));
