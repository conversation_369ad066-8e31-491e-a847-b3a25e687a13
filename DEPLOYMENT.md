# RELIFE Medical Technologies - Git-Based Deployment Guide

## 🚀 Quick Deployment for Shared Hosting

This guide covers deploying the RELIFE application from Git to shared hosting environments without Node.js support.

### 📋 Prerequisites

**Server Requirements:**
- PHP 8.2 or higher
- MySQL 5.7+ or MariaDB 10.3+
- Apache/Nginx web server
- Composer installed
- SSL certificate (recommended)

**Required PHP Extensions:**
- BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML, GD/Imagick

### 🔄 Deployment Steps

#### 1. Clone Repository
```bash
# Clone the repository to your hosting account
git clone https://github.com/your-username/relife.git
cd relife

# Or if updating existing deployment
git pull origin main
```

#### 2. Install PHP Dependencies
```bash
# Install Composer dependencies (vendor directory excluded from Git)
composer install --optimize-autoloader --no-dev --no-interaction

# Verify installation
php artisan --version
```

#### 3. Environment Configuration
```bash
# Copy environment template (create your own .env)
cp .env.example .env

# Edit .env with your production settings:
# - Database credentials
# - Email configuration  
# - APP_URL=https://re-life.ca
# - APP_ENV=production
# - APP_DEBUG=false

# Generate application key
php artisan key:generate
```

#### 4. Database Setup
```bash
# Run migrations
php artisan migrate --force

# Seed initial data (optional)
php artisan db:seed --force
```

#### 5. Storage & Permissions
```bash
# Create storage link
php artisan storage:link

# Set proper permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

#### 6. Production Optimization
```bash
# Cache configurations for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

#### 7. Web Server Configuration
Point your domain to the `public/` directory. The included `.htaccess` handles:
- Laravel admin routes (`/admin/*`)
- API routes (`/api/*`) 
- React SPA routing (all other routes)

### 🏗️ Architecture Overview

**Frontend:** React SPA (pre-built assets included in `/public/build/`)
**Backend:** Laravel 11 with Filament Admin Panel
**Database:** MySQL/MariaDB
**Deployment:** Git-based with Composer dependency installation

### 📁 Key Directories

- `/public/build/` - Pre-built React application assets (included in Git)
- `/vendor/` - PHP dependencies (excluded from Git, installed via Composer)
- `/node_modules/` - Node.js dependencies (excluded from Git, not needed on server)
- `/storage/` - Laravel storage (logs, cache, uploads)
- `/app/` - Laravel application code

### 🔧 Production URLs

- **Frontend:** https://re-life.ca
- **API:** https://re-life.ca/api/v1/
- **Admin Panel:** https://re-life.ca/admin

### 🆘 Troubleshooting

**500 Error:** Check file permissions and `.env` configuration
**Database Issues:** Verify credentials in `.env`
**Missing Assets:** Ensure `storage:link` was run
**React Routes 404:** Check `.htaccess` configuration

### 📞 Support

**RELIFE Medical Technologies**
- Email: <EMAIL>
- Phone: +****************
- Address: EDGEROOK DR TORONTO ON M9V 5E8 CANADA

---

*This deployment package includes pre-built React assets for shared hosting environments without Node.js support.*
